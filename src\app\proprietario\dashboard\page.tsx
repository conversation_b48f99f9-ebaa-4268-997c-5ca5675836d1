'use client';

import React, { useEffect, useState } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { useGerenciamentoAgendamentos } from '@/hooks/useGerenciamentoAgendamentos';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { EstatisticasAgendamentos } from '@/components/agendamentos/EstatisticasAgendamentos';
import { CardAgendamento } from '@/components/agendamentos/CardAgendamento';

// Novos componentes do dashboard melhorado
import { VerificacaoEmpresa } from '@/components/proprietario/VerificacaoEmpresa';
import { InformacoesEmpresa } from '@/components/proprietario/InformacoesEmpresa';
import { MetricasNegocio } from '@/components/proprietario/MetricasNegocio';
import { StatusPlanoSaas } from '@/components/proprietario/StatusPlanoSaas';
import { AlertasDashboard } from '@/components/proprietario/AlertasDashboard';
import { AcoesRapidas } from '@/components/proprietario/AcoesRapidas';

import Link from 'next/link';
import { parseISO, startOfWeek, endOfWeek } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export default function ProprietarioDashboardPage() {
  return (
    <ProtectedRoute requiredRole="Proprietario">
      <VerificacaoEmpresa redirecionarSeNaoTiver={false} mostrarMensagem={true}>
        <ProprietarioDashboard />
      </VerificacaoEmpresa>
    </ProtectedRoute>
  );
}

function ProprietarioDashboard() {
  const { user } = useAuth();
  const [periodoSelecionado, setPeriodoSelecionado] = useState<'hoje' | 'semana' | 'mes'>('hoje');
  const [abaSelecionada, setAbaSelecionada] = useState<'visao-geral' | 'agendamentos' | 'configuracoes'>('visao-geral');
  const [autoRefreshAtivo, setAutoRefreshAtivo] = useState(true);

  const {
    agendamentos,
    estatisticas,
    loading,
    buscarAgendamentos,
    confirmarAgendamento,
    recusarAgendamento
  } = useGerenciamentoAgendamentos();

  // Carregar agendamentos ao montar o componente
  useEffect(() => {
    // Converter período para filtros de data
    const agora = new Date();
    let filtros = {};

    switch (periodoSelecionado) {
      case 'hoje': {
        filtros = {
          data_inicio: agora.toISOString().split('T')[0],
          data_fim: agora.toISOString().split('T')[0]
        };
        break;
      }
      case 'semana': {
        const inicioSemana = new Date(agora);
        inicioSemana.setDate(agora.getDate() - agora.getDay());
        const fimSemana = new Date(inicioSemana);
        fimSemana.setDate(inicioSemana.getDate() + 6);
        filtros = {
          data_inicio: inicioSemana.toISOString().split('T')[0],
          data_fim: fimSemana.toISOString().split('T')[0]
        };
        break;
      }
      case 'mes': {
        const inicioMes = new Date(agora.getFullYear(), agora.getMonth(), 1);
        const fimMes = new Date(agora.getFullYear(), agora.getMonth() + 1, 0);
        filtros = {
          data_inicio: inicioMes.toISOString().split('T')[0],
          data_fim: fimMes.toISOString().split('T')[0]
        };
        break;
      }
    }

    buscarAgendamentos(filtros);
  }, [buscarAgendamentos, periodoSelecionado]);

  // Auto-refresh a cada 5 minutos
  useEffect(() => {
    if (!autoRefreshAtivo) return;

    const interval = setInterval(() => {
      const agora = new Date();
      let filtros = {};

      switch (periodoSelecionado) {
        case 'hoje': {
          filtros = {
            data_inicio: agora.toISOString().split('T')[0],
            data_fim: agora.toISOString().split('T')[0]
          };
          break;
        }
        case 'semana': {
          const inicioSemana = new Date(agora);
          inicioSemana.setDate(agora.getDate() - agora.getDay());
          const fimSemana = new Date(inicioSemana);
          fimSemana.setDate(inicioSemana.getDate() + 6);
          filtros = {
            data_inicio: inicioSemana.toISOString().split('T')[0],
            data_fim: fimSemana.toISOString().split('T')[0]
          };
          break;
        }
        case 'mes': {
          const inicioMes = new Date(agora.getFullYear(), agora.getMonth(), 1);
          const fimMes = new Date(agora.getFullYear(), agora.getMonth() + 1, 0);
          filtros = {
            data_inicio: inicioMes.toISOString().split('T')[0],
            data_fim: fimMes.toISOString().split('T')[0]
          };
          break;
        }
      }

      buscarAgendamentos(filtros);
    }, 5 * 60 * 1000); // 5 minutos

    return () => clearInterval(interval);
  }, [autoRefreshAtivo, periodoSelecionado, buscarAgendamentos]);

  // Filtrar agendamentos por período
  const obterAgendamentosPorPeriodo = () => {
    const agora = new Date();

    switch (periodoSelecionado) {
      case 'hoje': {
        return agendamentos.filter(a => {
          const dataAgendamento = parseISO(a.data_hora_inicio);
          return dataAgendamento.toDateString() === agora.toDateString();
        });
      }
      case 'semana': {
        const inicioSemana = startOfWeek(agora, { locale: ptBR });
        const fimSemana = endOfWeek(agora, { locale: ptBR });
        return agendamentos.filter(a => {
          const dataAgendamento = parseISO(a.data_hora_inicio);
          return dataAgendamento >= inicioSemana && dataAgendamento <= fimSemana;
        });
      }
      case 'mes': {
        return agendamentos.filter(a => {
          const dataAgendamento = parseISO(a.data_hora_inicio);
          return dataAgendamento.getMonth() === agora.getMonth() &&
                 dataAgendamento.getFullYear() === agora.getFullYear();
        });
      }
      default:
        return agendamentos;
    }
  };

  const agendamentosPendentes = agendamentos.filter(a => a.status_agendamento === 'Pendente');

  // Agendamentos próximos ao prazo (2 horas)
  const agendamentosProximoPrazo = agendamentosPendentes.filter(a => {
    const prazo = parseISO(a.prazo_confirmacao);
    const agora = new Date();
    const duasHoras = 2 * 60 * 60 * 1000;
    return (prazo.getTime() - agora.getTime()) < duasHoras && (prazo.getTime() - agora.getTime()) > 0;
  });

  // Função para obter texto do período
  const obterTextoPeriodo = (periodo: string) => {
    switch (periodo) {
      case 'hoje': return 'Hoje';
      case 'semana': return 'Semana';
      case 'mes': return 'Mês';
      default: return 'Hoje';
    }
  };

  return (
    <div className="min-h-screen bg-[var(--background)]">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-[var(--border-color)]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-[var(--text-primary)]">
                Dashboard do Proprietário
              </h1>
              <p className="text-[var(--text-secondary)]">
                Bem-vindo, {user?.name ?? 'Proprietário'}!
              </p>
            </div>
            <div className="flex items-center gap-4">
              {/* Controle de auto-refresh */}
              <button
                onClick={() => setAutoRefreshAtivo(!autoRefreshAtivo)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  autoRefreshAtivo
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-600'
                }`}
                title={autoRefreshAtivo ? 'Auto-refresh ativo (5min)' : 'Auto-refresh desativado'}
              >
                {autoRefreshAtivo ? '🔄 Auto' : '⏸️ Manual'}
              </button>

              {/* Seletor de período */}
              <div className="flex bg-[var(--surface)] rounded-lg p-1">
                {(['hoje', 'semana', 'mes'] as const).map((periodo) => (
                  <button
                    key={periodo}
                    onClick={() => setPeriodoSelecionado(periodo)}
                    className={`px-3 py-1 rounded text-sm transition-colors ${
                      periodoSelecionado === periodo
                        ? 'bg-[var(--primary)] text-white'
                        : 'text-[var(--text-secondary)] hover:text-[var(--text-primary)]'
                    }`}
                  >
                    {obterTextoPeriodo(periodo)}
                  </button>
                ))}
              </div>

              {/* Navegação por abas */}
              <div className="flex bg-[var(--surface)] rounded-lg p-1">
                {([
                  { key: 'visao-geral', label: 'Visão Geral', icon: '📊' },
                  { key: 'agendamentos', label: 'Agendamentos', icon: '📅' },
                  { key: 'configuracoes', label: 'Configurações', icon: '⚙️' }
                ] as const).map((aba) => (
                  <button
                    key={aba.key}
                    onClick={() => setAbaSelecionada(aba.key)}
                    className={`px-3 py-1 rounded-md text-sm font-medium transition-colors flex items-center gap-1 ${
                      abaSelecionada === aba.key
                        ? 'bg-[var(--primary)] text-[var(--text-on-primary)]'
                        : 'text-[var(--text-secondary)] hover:text-[var(--text-primary)]'
                    }`}
                  >
                    <span>{aba.icon}</span>
                    <span className="hidden sm:inline">{aba.label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Alertas inteligentes */}
        <div className="mb-8">
          <AlertasDashboard
            agendamentosProximoPrazo={agendamentosProximoPrazo}
            className="mb-6"
          />
        </div>

        {/* Conteúdo das abas */}
        {abaSelecionada === 'visao-geral' && (
          <div className="space-y-8">
            {/* Informações da empresa */}
            <InformacoesEmpresa />

            {/* Métricas de negócio */}
            <MetricasNegocio />

            {/* Status do plano SaaS */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                {/* Estatísticas do período */}
                {estatisticas && (
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-4">
                      📈 Estatísticas - {obterTextoPeriodo(periodoSelecionado)}
                    </h3>
                    <EstatisticasAgendamentos
                      estatisticas={estatisticas}
                      loading={loading}
                      periodo={periodoSelecionado}
                    />
                  </div>
                )}
              </div>
              <div>
                <StatusPlanoSaas />
              </div>
            </div>

            {/* Ações rápidas */}
            <AcoesRapidas />
          </div>
        )}

        {abaSelecionada === 'agendamentos' && (
          <div className="space-y-8">
            {/* Estatísticas do Período */}
            {estatisticas && (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-[var(--text-primary)]">
                    📊 Estatísticas - {obterTextoPeriodo(periodoSelecionado)}
                  </h2>
                </div>
                <EstatisticasAgendamentos
                  estatisticas={estatisticas}
                  loading={loading}
                  periodo={periodoSelecionado}
                />
              </div>
            )}

            {/* Agendamentos Pendentes */}
            {agendamentosPendentes.length > 0 && (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-[var(--text-primary)]">
                    ⏳ Solicitações Pendentes ({agendamentosPendentes.length})
                  </h2>
                  <Link href="/proprietario/agendamentos">
                    <Button variant="outline" size="sm">
                      Ver Todos
                    </Button>
                  </Link>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {agendamentosPendentes.slice(0, 6).map(agendamento => (
                    <CardAgendamento
                      key={agendamento.agendamento_id}
                      agendamento={agendamento}
                      onConfirmar={confirmarAgendamento}
                      onRecusar={recusarAgendamento}
                      loading={loading}
                      userRole="Proprietario"
                      mostrarAcoes={true}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Agendamentos do Dia */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-[var(--text-primary)]">
                  📅 Agendamentos de Hoje
                </h2>
                <Link href="/proprietario/agendamentos">
                  <Button variant="outline" size="sm">
                    Ver Agenda Completa
                  </Button>
                </Link>
              </div>

              {(() => {
                const agendamentosHoje = obterAgendamentosPorPeriodo().filter(a => {
                  const hoje = new Date();
                  const dataAgendamento = parseISO(a.data_hora_inicio);
                  return dataAgendamento.toDateString() === hoje.toDateString();
                });

                if (agendamentosHoje.length === 0) {
                  return (
                    <Card>
                      <CardContent className="p-8 text-center">
                        <div className="text-[var(--text-secondary)]">
                          <svg className="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          <p className="text-lg font-medium">Nenhum agendamento para hoje</p>
                          <p className="text-sm">Aproveite para organizar seu estabelecimento!</p>
                        </div>
                      </CardContent>
                    </Card>
                  );
                }

                return (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {agendamentosHoje.slice(0, 8).map(agendamento => (
                      <CardAgendamento
                        key={agendamento.agendamento_id}
                        agendamento={agendamento}
                        onConfirmar={confirmarAgendamento}
                        onRecusar={recusarAgendamento}
                        loading={loading}
                        userRole="Proprietario"
                        mostrarAcoes={true}
                      />
                    ))}
                  </div>
                );
              })()}
            </div>
          </div>
        )}

        {abaSelecionada === 'configuracoes' && (
          <div className="space-y-8">
            {/* Informações da empresa */}
            <InformacoesEmpresa />

            {/* Status do plano SaaS */}
            <StatusPlanoSaas />

            {/* Ações de configuração */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Configurações da Empresa */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <span className="text-2xl mr-2">🏢</span>
                    Empresa
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-[var(--text-secondary)] mb-4">
                    Dados básicos, logo e informações de contato
                  </p>
                  <Link href="/proprietario/configuracoes?tab=empresa">
                    <Button className="w-full">
                      Configurar Empresa
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              {/* Pagamentos */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <span className="text-2xl mr-2">💳</span>
                    Pagamentos
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-[var(--text-secondary)] mb-4">
                    Stripe Connect e configurações de pagamento
                  </p>
                  <Link href="/proprietario/configuracoes?tab=pagamentos">
                    <Button className="w-full">
                      Configurar Pagamentos
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              {/* Horários */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <span className="text-2xl mr-2">⏰</span>
                    Horários
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-[var(--text-secondary)] mb-4">
                    Horários de funcionamento e bloqueios
                  </p>
                  <Link href="/proprietario/horarios">
                    <Button className="w-full">
                      Gerenciar Horários
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              {/* Serviços */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <span className="text-2xl mr-2">🛠️</span>
                    Serviços
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-[var(--text-secondary)] mb-4">
                    Cadastrar e gerenciar serviços oferecidos
                  </p>
                  <Link href="/proprietario/servicos">
                    <Button className="w-full">
                      Gerenciar Serviços
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              {/* Colaboradores */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <span className="text-2xl mr-2">👥</span>
                    Colaboradores
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-[var(--text-secondary)] mb-4">
                    Convidar e gerenciar equipe de trabalho
                  </p>
                  <Link href="/proprietario/colaboradores">
                    <Button className="w-full">
                      Gerenciar Colaboradores
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              {/* Notificações */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <span className="text-2xl mr-2">🔔</span>
                    Notificações
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-[var(--text-secondary)] mb-4">
                    Preferências de notificação e alertas
                  </p>
                  <Link href="/proprietario/configuracoes?tab=notificacoes">
                    <Button className="w-full">
                      Configurar Notificações
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
