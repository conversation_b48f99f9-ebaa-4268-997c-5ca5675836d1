import { NextResponse } from 'next/server';
import { createAdminClient } from '@/utils/supabase/server';
import { cookies } from 'next/headers';
import jwt from 'jsonwebtoken';

export async function GET() {
  try {
    // Verificação manual de autenticação usando cookies
    const cookieStore = await cookies();

    // Buscar token de autenticação nos cookies
    let authToken = null;
    let userId = null;

    // Tentar diferentes formatos de cookies
    const possibleTokenCookies = [
      'sb-tlbpsdgoklkekoxzmzlo-auth-token',
      'sb-access-token',
      'supabase-auth-token'
    ];

    for (const cookieName of possibleTokenCookies) {
      const cookie = cookieStore.get(cookieName);
      if (cookie?.value) {
        authToken = cookie.value;
        break;
      }
    }

    if (!authToken) {
      return NextResponse.json(
        { success: false, error: 'Token de autenticação não encontrado' },
        { status: 401 }
      );
    }

    // Decodificar JWT para obter user ID
    try {
      const decoded = jwt.decode(authToken) as any;
      if (decoded && decoded.sub) {
        userId = decoded.sub;
      } else {
        return NextResponse.json(
          { success: false, error: 'Token inválido' },
          { status: 401 }
        );
      }
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Token inválido' },
        { status: 401 }
      );
    }

    // Buscar empresa do proprietário usando cliente admin para evitar problemas de RLS
    const supabaseAdmin = createAdminClient();
    const { data: empresa, error: empresaError } = await supabaseAdmin
      .from('empresas')
      .select('empresa_id, nome_empresa, status')
      .eq('proprietario_user_id', userId)
      .eq('status', 'ativo')
      .single();

    if (empresaError) {
      console.error('Erro ao buscar empresa do proprietário:', empresaError);
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    if (!empresa) {
      return NextResponse.json(
        { success: false, error: 'Nenhuma empresa encontrada para este proprietário' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      empresa
    });

  } catch (error: unknown) {
    console.error('Erro geral na API de empresa do proprietário:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
